package com.cnpc.why.controller;

import com.cnpc.why.dto.*;
import com.cnpc.why.request.DailyCustomersRequest;
import com.cnpc.why.request.ValuableCustomerListRequest;
import com.cnpc.why.service.CustomerService;
import com.cnpc.why.service.DashboardService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * 智能分析控制器
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    @Autowired
    private CustomerService customerService;

    /**
     * 获取仪表板数据
     *
     * @return 仪表板数据
     */
    @PostMapping("/data")
    public ResponseDTO<DashboardDataDTO> data() {
        return dashboardService.getDashboardData();
    }

    /**
     * 获取高价值客户列表
     *
     * @param request 查询请求
     * @return 高价值客户分页数据
     */
    @PostMapping("/valuable-customers")
    public ResponseDTO<ValuableCustomerPageDTO> valuableCustomers(@RequestBody ValuableCustomerListRequest request) {
        return dashboardService.getValuableCustomers(request);
    }

    /**
     * 获取指定日期新增客户详情
     *
     * @param request 查询请求
     * @return 客户列表
     */
    @PostMapping("/daily-customers")
    public ResponseDTO<List<CustomerDTO>> dailyCustomers(@RequestBody @Valid DailyCustomersRequest request) {
        return customerService.getDailyCustomers(request);
    }

    /**
     * 导出高价值客户Excel
     *
     * @return Excel文件
     */
    @PostMapping("/export-valuable-customers")
    public ResponseEntity<byte[]> exportValuableCustomers() throws IOException {
        List<ValuableCustomerDTO> customers = dashboardService.exportValuableCustomers();

        // 创建Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("高价值客户");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"客户姓名", "公司名称", "互动次数", "上次联系时间"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }

        // 填充数据
        for (int i = 0; i < customers.size(); i++) {
            Row row = sheet.createRow(i + 1);
            ValuableCustomerDTO customer = customers.get(i);
            
            row.createCell(0).setCellValue(customer.getName());
            row.createCell(1).setCellValue(customer.getCompanyName());
            row.createCell(2).setCellValue(customer.getInteractionCount());
            row.createCell(3).setCellValue(customer.getLastInteractionTime());
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 将工作簿写入字节数组
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        // 设置响应头
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
        responseHeaders.setContentDispositionFormData("attachment", "valuable_customers.xlsx");

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(outputStream.toByteArray());
    }
}
