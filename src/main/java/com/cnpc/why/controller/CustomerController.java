package com.cnpc.why.controller;

import com.cnpc.why.dto.CustomerDTO;
import com.cnpc.why.dto.CustomerOptionDTO;
import com.cnpc.why.dto.CustomerPageDTO;
import com.cnpc.why.dto.ResponseDTO;
import com.cnpc.why.request.*;
import com.cnpc.why.service.CustomerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 客户管理控制器
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Autowired
    private CustomerService customerService;

    /**
     * 分页查询客户列表
     *
     * @param request 查询请求
     * @return 客户分页数据
     */
    @PostMapping("/list")
    public ResponseDTO<CustomerPageDTO> list(@RequestBody CustomerListRequest request) {
        return customerService.listCustomers(request);
    }

    /**
     * 新增客户
     *
     * @param request 新增请求
     * @return 响应结果
     */
    @PostMapping("/add")
    public ResponseDTO<Void> add(@RequestBody @Valid CustomerAddRequest request) {
        return customerService.addCustomer(request);
    }

    /**
     * 获取客户详情
     *
     * @param request 详情请求
     * @return 客户详情
     */
    @PostMapping("/detail")
    public ResponseDTO<CustomerDTO> detail(@RequestBody @Valid CustomerDetailRequest request) {
        return customerService.getCustomerDetail(request);
    }

    /**
     * 更新客户信息
     *
     * @param request 更新请求
     * @return 响应结果
     */
    @PostMapping("/update")
    public ResponseDTO<Void> update(@RequestBody @Valid CustomerUpdateRequest request) {
        return customerService.updateCustomer(request);
    }

    /**
     * 删除客户
     *
     * @param request 删除请求
     * @return 响应结果
     */
    @PostMapping("/delete")
    public ResponseDTO<Void> delete(@RequestBody @Valid CustomerDeleteRequest request) {
        return customerService.deleteCustomer(request);
    }

    /**
     * 获取客户选项列表
     *
     * @return 客户选项列表
     */
    @PostMapping("/options")
    public ResponseDTO<List<CustomerOptionDTO>> options() {
        return customerService.getCustomerOptions();
    }
}
