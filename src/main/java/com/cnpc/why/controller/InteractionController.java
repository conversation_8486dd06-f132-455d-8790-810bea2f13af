package com.cnpc.why.controller;

import com.cnpc.why.dto.InteractionDTO;
import com.cnpc.why.dto.InteractionPageDTO;
import com.cnpc.why.dto.ResponseDTO;
import com.cnpc.why.request.*;
import com.cnpc.why.service.InteractionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 互动记录管理控制器
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@RestController
@RequestMapping("/interaction")
public class InteractionController {

    @Autowired
    private InteractionService interactionService;

    /**
     * 分页查询互动记录列表
     *
     * @param request 查询请求
     * @return 互动记录分页数据
     */
    @PostMapping("/list")
    public ResponseDTO<InteractionPageDTO> list(@RequestBody InteractionListRequest request) {
        return interactionService.listInteractions(request);
    }

    /**
     * 新增互动记录
     *
     * @param request 新增请求
     * @return 响应结果
     */
    @PostMapping("/add")
    public ResponseDTO<Void> add(@RequestBody @Valid InteractionAddRequest request) {
        return interactionService.addInteraction(request);
    }

    /**
     * 获取互动记录详情
     *
     * @param request 详情请求
     * @return 互动记录详情
     */
    @PostMapping("/detail")
    public ResponseDTO<InteractionDTO> detail(@RequestBody @Valid InteractionDetailRequest request) {
        return interactionService.getInteractionDetail(request);
    }

    /**
     * 更新互动记录
     *
     * @param request 更新请求
     * @return 响应结果
     */
    @PostMapping("/update")
    public ResponseDTO<Void> update(@RequestBody @Valid InteractionUpdateRequest request) {
        return interactionService.updateInteraction(request);
    }

    /**
     * 删除互动记录
     *
     * @param request 删除请求
     * @return 响应结果
     */
    @PostMapping("/delete")
    public ResponseDTO<Void> delete(@RequestBody @Valid InteractionDeleteRequest request) {
        return interactionService.deleteInteraction(request);
    }
}
