package com.cnpc.why.controller;

import com.cnpc.why.dto.LoginDTO;
import com.cnpc.why.dto.ResponseDTO;
import com.cnpc.why.request.UserLoginRequest;
import com.cnpc.why.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    @PostMapping("/login")
    public ResponseDTO<LoginDTO> login(@RequestBody @Valid UserLoginRequest request) {
        return userService.login(request);
    }
}
