package com.cnpc.why.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新互动记录请求对象
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class InteractionUpdateRequest {

    /**
     * 互动记录ID
     */
    @NotNull(message = "互动记录ID不能为空")
    private Long id;

    /**
     * 关联客户ID
     */
    @NotNull(message = "关联客户ID不能为空")
    private Long customerId;

    /**
     * 互动类型
     */
    @NotNull(message = "互动类型不能为空")
    private Integer type;

    /**
     * 内容摘要
     */
    @NotBlank(message = "内容摘要不能为空")
    private String summary;

    /**
     * 下次跟进时间
     */
    private String nextFollowUpTime;
}
