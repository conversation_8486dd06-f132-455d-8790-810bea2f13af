package com.cnpc.why.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新客户信息请求对象
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class CustomerUpdateRequest {

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空")
    private Long id;

    /**
     * 客户姓名
     */
    @NotBlank(message = "客户姓名不能为空")
    private String name;

    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空")
    private String companyName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 状态
     */
    @NotNull(message = "客户状态不能为空")
    private Integer status;

    /**
     * 来源
     */
    private String source;

    /**
     * 备注
     */
    private String remark;
}
