package com.cnpc.why.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增互动记录请求对象
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class InteractionAddRequest {

    /**
     * 关联客户ID
     */
    @NotNull(message = "关联客户ID不能为空")
    private Long customerId;

    /**
     * 互动类型，0=电话，1=邮件，2=会议
     */
    @NotNull(message = "互动类型不能为空")
    private Integer type;

    /**
     * 内容摘要
     */
    @NotBlank(message = "内容摘要不能为空")
    private String summary;

    /**
     * 下次跟进时间，格式：YYYY-MM-DD HH:mm:ss
     */
    private String nextFollowUpTime;
}
