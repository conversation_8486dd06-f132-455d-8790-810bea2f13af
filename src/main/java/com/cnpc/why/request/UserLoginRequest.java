package com.cnpc.why.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 用户登录请求对象
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class UserLoginRequest {

    /**
     * 用户账号
     */
    @NotBlank(message = "用户账号不能为空")
    private String username;

    /**
     * 用户密码
     */
    @NotBlank(message = "用户密码不能为空")
    private String password;
}
