package com.cnpc.why.request;

import lombok.Data;

/**
 * 分页查询互动记录列表请求对象
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class InteractionListRequest {

    /**
     * 当前页码，默认1
     */
    private Long pageNum = 1L;

    /**
     * 每页大小，默认10
     */
    private Long pageSize = 10L;

    /**
     * 互动类型筛选，0=电话，1=邮件，2=会议
     */
    private Integer type;

    /**
     * 关联客户ID筛选
     */
    private Long customerId;

    /**
     * 关键词搜索，支持内容摘要模糊查询
     */
    private String keyword;
}
