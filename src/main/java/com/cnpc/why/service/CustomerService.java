package com.cnpc.why.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.why.dao.CustomerDao;
import com.cnpc.why.dto.*;
import com.cnpc.why.entity.CustomerEntity;
import com.cnpc.why.exception.BizException;
import com.cnpc.why.request.*;
import com.cnpc.why.util.DateUtil;
import com.cnpc.why.util.UserContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户服务类
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Service
public class CustomerService {

    @Autowired
    private CustomerDao customerDao;

    /**
     * 分页查询客户列表
     *
     * @param request 查询请求
     * @return 客户分页数据
     */
    public ResponseDTO<CustomerPageDTO> listCustomers(CustomerListRequest request) {
        Page<CustomerDTO> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<CustomerDTO> result = customerDao.selectCustomerPage(page, request.getStatus(), request.getKeyword());

        CustomerPageDTO pageDTO = new CustomerPageDTO();
        pageDTO.setRows(result.getRecords());
        pageDTO.setPageNum(result.getCurrent());
        pageDTO.setPageSize(result.getSize());
        pageDTO.setPageSum(result.getPages());
        pageDTO.setTotal(result.getTotal());

        return ResponseDTO.success(pageDTO);
    }

    /**
     * 新增客户
     *
     * @param request 新增请求
     * @return 响应结果
     */
    public ResponseDTO<Void> addCustomer(CustomerAddRequest request) {
        CustomerEntity customer = new CustomerEntity();
        BeanUtils.copyProperties(request, customer);
        customer.setCreatedTime(LocalDateTime.now());
        customer.setUpdatedTime(LocalDateTime.now());
        Long currentUserId = UserContext.getCurrentUserId();
        customer.setCreatedBy(currentUserId != null ? currentUserId : 1L);
        customer.setUpdatedBy(currentUserId != null ? currentUserId : 1L);

        customerDao.insert(customer);
        return ResponseDTO.success();
    }

    /**
     * 获取客户详情
     *
     * @param request 详情请求
     * @return 客户详情
     */
    public ResponseDTO<CustomerDTO> getCustomerDetail(CustomerDetailRequest request) {
        CustomerEntity customer = customerDao.selectById(request.getId());
        if (customer == null || customer.getIsDeleted() == 1) {
            throw new BizException("002004", "客户不存在");
        }

        CustomerDTO customerDTO = new CustomerDTO();
        BeanUtils.copyProperties(customer, customerDTO);
        customerDTO.setCreatedTime(DateUtil.formatDateTime(customer.getCreatedTime()));
        customerDTO.setUpdatedTime(DateUtil.formatDateTime(customer.getUpdatedTime()));

        return ResponseDTO.success(customerDTO);
    }

    /**
     * 更新客户信息
     *
     * @param request 更新请求
     * @return 响应结果
     */
    public ResponseDTO<Void> updateCustomer(CustomerUpdateRequest request) {
        CustomerEntity customer = customerDao.selectById(request.getId());
        if (customer == null || customer.getIsDeleted() == 1) {
            throw new BizException("002004", "客户不存在");
        }

        BeanUtils.copyProperties(request, customer);
        customer.setUpdatedTime(LocalDateTime.now());
        Long currentUserId = UserContext.getCurrentUserId();
        customer.setUpdatedBy(currentUserId != null ? currentUserId : 1L);

        customerDao.updateById(customer);
        return ResponseDTO.success();
    }

    /**
     * 删除客户
     *
     * @param request 删除请求
     * @return 响应结果
     */
    public ResponseDTO<Void> deleteCustomer(CustomerDeleteRequest request) {
        CustomerEntity customer = customerDao.selectById(request.getId());
        if (customer == null || customer.getIsDeleted() == 1) {
            throw new BizException("002004", "客户不存在");
        }

        customer.setIsDeleted(1);
        customer.setUpdatedTime(LocalDateTime.now());
        Long currentUserId = UserContext.getCurrentUserId();
        customer.setUpdatedBy(currentUserId != null ? currentUserId : 1L);

        customerDao.updateById(customer);
        return ResponseDTO.success();
    }

    /**
     * 获取客户选项列表
     *
     * @return 客户选项列表
     */
    public ResponseDTO<List<CustomerOptionDTO>> getCustomerOptions() {
        List<CustomerOptionDTO> options = customerDao.selectCustomerOptions();
        return ResponseDTO.success(options);
    }

    /**
     * 根据日期查询新增客户列表
     *
     * @param request 查询请求
     * @return 客户列表
     */
    public ResponseDTO<List<CustomerDTO>> getDailyCustomers(DailyCustomersRequest request) {
        List<CustomerDTO> customers = customerDao.selectCustomersByDate(DateUtil.parseDate(request.getDate()));
        return ResponseDTO.success(customers);
    }
}
