package com.cnpc.why.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.why.dao.CustomerDao;
import com.cnpc.why.dao.DashboardDao;
import com.cnpc.why.dto.*;
import com.cnpc.why.request.ValuableCustomerListRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 仪表板服务类
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Service
public class DashboardService {

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private DashboardDao dashboardDao;

    /**
     * 获取仪表板数据
     *
     * @return 仪表板数据
     */
    public ResponseDTO<DashboardDataDTO> getDashboardData() {
        DashboardDataDTO dashboardData = new DashboardDataDTO();

        // 获取预警信息
        DashboardDataDTO.AlertsDTO alerts = new DashboardDataDTO.AlertsDTO();
        Double lostCustomerRate = customerDao.selectLostCustomerRate();
        Integer noNewCustomerDays = customerDao.selectNoNewCustomerDays();
        
        alerts.setLostCustomerRate(lostCustomerRate);
        alerts.setNoNewCustomerDays(noNewCustomerDays);
        // 判断是否显示预警：已流失客户占比 > 30% 或 7天内无新增客户
        alerts.setShowAlert(lostCustomerRate > 30.0 || noNewCustomerDays > 7);
        
        dashboardData.setAlerts(alerts);

        // 获取客户趋势数据
        List<CustomerDao.CustomerTrendData> trendDataList = customerDao.selectCustomerTrendData();
        DashboardDataDTO.CustomerTrendDTO customerTrend = new DashboardDataDTO.CustomerTrendDTO();
        
        // 生成近30日的完整日期列表
        List<String> dates = new ArrayList<>();
        List<Integer> counts = new ArrayList<>();
        
        // 将查询结果转换为Map，便于查找
        Map<String, Integer> trendMap = trendDataList.stream()
                .collect(Collectors.toMap(
                        CustomerDao.CustomerTrendData::getDate,
                        CustomerDao.CustomerTrendData::getCount
                ));
        
        // 生成近30日的完整数据
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(29);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            String dateStr = date.format(formatter);
            dates.add(dateStr);
            counts.add(trendMap.getOrDefault(dateStr, 0));
        }
        
        customerTrend.setDates(dates);
        customerTrend.setCounts(counts);
        dashboardData.setCustomerTrend(customerTrend);

        return ResponseDTO.success(dashboardData);
    }

    /**
     * 获取高价值客户列表
     *
     * @param request 查询请求
     * @return 高价值客户分页数据
     */
    public ResponseDTO<ValuableCustomerPageDTO> getValuableCustomers(ValuableCustomerListRequest request) {
        Page<ValuableCustomerDTO> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<ValuableCustomerDTO> result = dashboardDao.selectValuableCustomerPage(page);

        ValuableCustomerPageDTO pageDTO = new ValuableCustomerPageDTO();
        pageDTO.setRows(result.getRecords());
        pageDTO.setPageNum(result.getCurrent());
        pageDTO.setPageSize(result.getSize());
        pageDTO.setPageSum(result.getPages());
        pageDTO.setTotal(result.getTotal());

        return ResponseDTO.success(pageDTO);
    }

    /**
     * 导出高价值客户Excel
     *
     * @return 高价值客户列表
     */
    public List<ValuableCustomerDTO> exportValuableCustomers() {
        return dashboardDao.selectAllValuableCustomers();
    }
}
