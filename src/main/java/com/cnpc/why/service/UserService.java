package com.cnpc.why.service;

import com.cnpc.why.dao.UserDao;
import com.cnpc.why.dto.LoginDTO;
import com.cnpc.why.dto.ResponseDTO;
import com.cnpc.why.dto.UserInfoDTO;
import com.cnpc.why.entity.UserEntity;
import com.cnpc.why.exception.BizException;
import com.cnpc.why.request.UserLoginRequest;
import com.cnpc.why.util.JwtUtil;
import com.cnpc.why.util.PasswordUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户服务类
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Service
public class UserService {

    @Autowired
    private UserDao userDao;

    /**
     * 用户登录
     *
     * @param request 登录请求
     * @return 登录响应
     */
    public ResponseDTO<LoginDTO> login(UserLoginRequest request) {
        // 根据用户名查询用户
        UserEntity user = userDao.selectByUsername(request.getUsername());
        if (user == null) {
            throw new BizException("001001", "用户名或密码错误");
        }

        // 验证密码
        if (!PasswordUtil.matches(request.getPassword(), user.getPassword())) {
            throw new BizException("001001", "用户名或密码错误");
        }

        // 生成JWT令牌
        String token = JwtUtil.generateToken(user.getId(), user.getUsername());

        // 构建用户信息
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRealName(user.getRealName());

        // 构建登录响应
        LoginDTO loginDTO = new LoginDTO();
        loginDTO.setToken(token);
        loginDTO.setUserInfo(userInfo);

        return ResponseDTO.success(loginDTO);
    }

    /**
     * 根据用户名查询用户信息
     *
     * @param username 用户名
     * @return 用户实体
     */
    public UserEntity getUserByUsername(String username) {
        return userDao.selectByUsername(username);
    }

    /**
     * 根据用户ID查询用户信息
     *
     * @param userId 用户ID
     * @return 用户实体
     */
    public UserEntity getUserById(Long userId) {
        return userDao.selectById(userId);
    }
}
