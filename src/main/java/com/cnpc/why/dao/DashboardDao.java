package com.cnpc.why.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.why.dto.ValuableCustomerDTO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 仪表板数据访问层
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Mapper
public interface DashboardDao {

    /**
     * 分页查询高价值客户列表
     *
     * @param page 分页对象
     * @return 高价值客户分页数据
     */
    IPage<ValuableCustomerDTO> selectValuableCustomerPage(Page<ValuableCustomerDTO> page);

    /**
     * 查询所有高价值客户列表（用于导出）
     *
     * @return 高价值客户列表
     */
    List<ValuableCustomerDTO> selectAllValuableCustomers();
}
