package com.cnpc.why.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.why.dto.CustomerDTO;
import com.cnpc.why.dto.CustomerOptionDTO;
import com.cnpc.why.entity.CustomerEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 客户数据访问层
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Mapper
public interface CustomerDao extends BaseMapper<CustomerEntity> {

    /**
     * 分页查询客户列表
     *
     * @param page    分页对象
     * @param status  客户状态
     * @param keyword 关键词
     * @return 客户分页数据
     */
    IPage<CustomerDTO> selectCustomerPage(Page<CustomerDTO> page, 
                                         @Param("status") Integer status, 
                                         @Param("keyword") String keyword);

    /**
     * 获取客户选项列表
     *
     * @return 客户选项列表
     */
    List<CustomerOptionDTO> selectCustomerOptions();

    /**
     * 根据日期查询新增客户列表
     *
     * @param date 查询日期
     * @return 客户列表
     */
    List<CustomerDTO> selectCustomersByDate(@Param("date") LocalDate date);

    /**
     * 统计已流失客户占比
     *
     * @return 已流失客户占比
     */
    Double selectLostCustomerRate();

    /**
     * 获取最近无新增客户天数
     *
     * @return 无新增客户天数
     */
    Integer selectNoNewCustomerDays();

    /**
     * 获取近30日新增客户趋势数据
     *
     * @return 趋势数据
     */
    List<CustomerTrendData> selectCustomerTrendData();

    /**
     * 客户趋势数据内部类
     */
    class CustomerTrendData {
        private String date;
        private Integer count;

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }
    }
}
