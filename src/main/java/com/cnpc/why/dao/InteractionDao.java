package com.cnpc.why.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cnpc.why.dto.InteractionDTO;
import com.cnpc.why.entity.InteractionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 互动记录数据访问层
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Mapper
public interface InteractionDao extends BaseMapper<InteractionEntity> {

    /**
     * 分页查询互动记录列表
     *
     * @param page       分页对象
     * @param type       互动类型
     * @param customerId 关联客户ID
     * @param keyword    关键词
     * @return 互动记录分页数据
     */
    IPage<InteractionDTO> selectInteractionPage(Page<InteractionDTO> page,
                                               @Param("type") Integer type,
                                               @Param("customerId") Long customerId,
                                               @Param("keyword") String keyword);

    /**
     * 根据ID查询互动记录详情
     *
     * @param id 互动记录ID
     * @return 互动记录详情
     */
    InteractionDTO selectInteractionDetailById(@Param("id") Long id);
}
