package com.cnpc.why.exception;

import com.cnpc.why.dto.ResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import javax.websocket.DecodeException;


/**
 * 全局异常统一处理
 *
 * <AUTHOR>
 * @date 2022/8/2 18:21
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@ControllerAdvice
public class CommonExceptionHandler {

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public ResponseDTO<Void> processBindException(HttpServletResponse response,
                                                  Exception e) {
        response.setStatus(200);
        BindException be = (BindException) e;
        return ResponseDTO.fail("S_" + getAppCode() + "_99999", be.getAllErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseDTO<Void> processValidException(HttpServletResponse response,
                                                   Exception e) {
        response.setStatus(200);
        return ResponseDTO.fail("S_" + getAppCode() + "_99999",
                ((MethodArgumentNotValidException) e).getBindingResult().getAllErrors().get(0).getDefaultMessage());
    }

    @ExceptionHandler(BizException.class)
    @ResponseBody
    public ResponseDTO<Void> processBizException(HttpServletResponse response,
                                                 Exception e) {
        BizException be = (BizException) e;
        response.setStatus(200);
        String code = be.getCode();
        if (StringUtils.isBlank(code)) {
            code = "B_" + getAppCode() + "_000000";
        } else if (!code.contains("_")) {
            code = "B_" + getAppCode() + "_" + code;
        }
        log.error("BusinessException:" + code, be);
        return ResponseDTO.fail(code, be.getMessage());

    }

    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseDTO<Void> processOther(HttpServletResponse response,
                                          Exception e) {
        response.setStatus(500);
        log.error("SysException:", e);
        return ResponseDTO.fail("S_" + getAppCode() + "_00000", "系统异常,请稍后重试");
    }

    @ExceptionHandler(DecodeException.class)
    @ResponseBody
    public ResponseDTO<Void> processDecodeException(HttpServletResponse response,
                                                    Exception e) {
        DecodeException be = (DecodeException) e;
        BizException bbe = null;
        if (be.getCause() != null && be.getCause() instanceof BizException) {
            bbe = (BizException) be.getCause();
            response.setStatus(200);
            log.error("BusinessException:" + bbe.getCode(), be);
            return ResponseDTO.fail(bbe.getCode(), bbe.getMessage());
        } else {
            response.setStatus(500);
            log.error("SysException:", e);
            return ResponseDTO.fail("S_" + getAppCode() + "_00001", "系统异常,请稍后重试");
        }
    }

    private String getAppCode() {
        return "WHY";
    }

}