package com.cnpc.why.exception;



import com.cnpc.why.dto.ResponseDTO;

public class BizException extends RuntimeException {

    private final String code;

    public BizException() {
        super();
        code = "S_" + getAppCode() + "_ERROR";
    }

    public BizException(String message) {
        super(message);
        code = "S_" + getAppCode() + "_ERROR";
    }

    public BizException(ResponseDTO<?> responseDTO) {
        super(responseDTO.getMessage());
        this.code = responseDTO.getErrorCode();
    }

    public BizException(String code, String message) {
        super(message);
        this.code = code;
    }


    public BizException(String message, Throwable cause) {
        super(message, cause);
        code = "S_" + getAppCode() + "_ERROR";
    }

    public BizException(Throwable cause) {
        super(cause);
        code = "S_" + getAppCode() + "_ERROR";
    }

    private String getAppCode() {
        return "WHY";
    }

    public String getCode() {
        return code;
    }
}
