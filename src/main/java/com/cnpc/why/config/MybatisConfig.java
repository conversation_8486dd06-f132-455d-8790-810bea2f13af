package com.cnpc.why.config;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis-Plus 配置类，用于配置 MyBatis-Plus 的拦截器。
 * <p>
 * 该类仅在配置文件中存在 spring.datasource.druid.url 属性时才会生效。
 * 主要功能是添加 MyBatis-Plus 的分页插件，支持 MySQL 数据库的分页操作。
 *
 * @return MybatisPlusInterceptor 返回配置好的 MyBatis-Plus 拦截器实例。
 */
@Configuration
@Slf4j
@ConditionalOnProperty(name = "spring.datasource.druid.url")
public class MybatisConfig {

    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL)); // 设置分页插件
        return interceptor;
    }

}
