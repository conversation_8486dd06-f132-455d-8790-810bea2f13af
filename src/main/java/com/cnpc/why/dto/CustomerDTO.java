package com.cnpc.why.dto;

import lombok.Data;

/**
 * 客户信息DTO
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class CustomerDTO {

    /**
     * 客户ID
     */
    private Long id;

    /**
     * 客户姓名
     */
    private String name;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 状态，0=潜在客户，1=已联系客户，2=已流失
     */
    private Integer status;

    /**
     * 来源
     */
    private String source;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 更新时间
     */
    private String updatedTime;
}
