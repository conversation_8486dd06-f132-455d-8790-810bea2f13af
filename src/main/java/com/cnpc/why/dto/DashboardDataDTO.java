package com.cnpc.why.dto;

import lombok.Data;

import java.util.List;

/**
 * 仪表板数据DTO
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class DashboardDataDTO {

    /**
     * 预警信息
     */
    private AlertsDTO alerts;

    /**
     * 客户趋势数据
     */
    private CustomerTrendDTO customerTrend;

    /**
     * 预警信息内部类
     */
    @Data
    public static class AlertsDTO {
        /**
         * 已流失客户占比
         */
        private Double lostCustomerRate;

        /**
         * 无新增客户天数
         */
        private Integer noNewCustomerDays;

        /**
         * 是否显示预警
         */
        private Boolean showAlert;
    }

    /**
     * 客户趋势数据内部类
     */
    @Data
    public static class CustomerTrendDTO {
        /**
         * 日期列表
         */
        private List<String> dates;

        /**
         * 对应日期的新增客户数量
         */
        private List<Integer> counts;
    }
}
