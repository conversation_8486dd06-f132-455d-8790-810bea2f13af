package com.cnpc.why.dto;

import lombok.Data;

/**
 * 互动记录信息DTO
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
public class InteractionDTO {

    /**
     * 互动记录ID
     */
    private Long id;

    /**
     * 互动类型，0=电话，1=邮件，2=会议
     */
    private Integer type;

    /**
     * 内容摘要
     */
    private String summary;

    /**
     * 下次跟进时间
     */
    private String nextFollowUpTime;

    /**
     * 关联客户ID
     */
    private Long customerId;

    /**
     * 关联客户姓名
     */
    private String customerName;

    /**
     * 创建时间
     */
    private String createdTime;

    /**
     * 更新时间
     */
    private String updatedTime;
}
