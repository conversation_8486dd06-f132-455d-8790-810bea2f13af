package com.cnpc.why.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 互动记录实体类
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Data
@TableName("interaction")
public class InteractionEntity {

    /**
     * 互动ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 互动类型：0-电话，1-邮件，2-会议
     */
    @TableField("type")
    private Integer type;

    /**
     * 内容摘要
     */
    @TableField("summary")
    private String summary;

    /**
     * 下次跟进时间
     */
    @TableField("next_follow_up_time")
    private LocalDateTime nextFollowUpTime;

    /**
     * 关联客户ID
     */
    @TableField("customer_id")
    private Long customerId;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    /**
     * 创建人ID
     */
    @TableField("created_by")
    private Long createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新人ID
     */
    @TableField("updated_by")
    private Long updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}
