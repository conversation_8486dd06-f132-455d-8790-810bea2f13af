package com.cnpc.why.interceptor;

import com.cnpc.why.exception.BizException;
import com.cnpc.why.util.JwtUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * JWT认证拦截器
 *
 * <AUTHOR>
 * @date 2023/12/01
 * @company 昆仑数智科技有限责任公司
 */
@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 获取Authorization头
        String authorization = request.getHeader("Authorization");
        
        if (StringUtils.isBlank(authorization)) {
            throw new BizException("001004", "登录令牌无效");
        }
        
        // 检查Bearer前缀
        if (!authorization.startsWith("Bearer ")) {
            throw new BizException("001004", "登录令牌无效");
        }
        
        // 提取token
        String token = authorization.substring(7);
        
        // 验证token
        if (!JwtUtil.verifyToken(token)) {
            throw new BizException("001005", "登录令牌已过期");
        }
        
        // 将用户信息存储到请求属性中，供后续使用
        Long userId = JwtUtil.getUserId(token);
        String username = JwtUtil.getUsername(token);
        request.setAttribute("userId", userId);
        request.setAttribute("username", username);
        
        return true;
    }
}
