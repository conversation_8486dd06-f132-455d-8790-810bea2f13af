-- 初始化用户数据
INSERT INTO `user` (`id`, `username`, `password`, `real_name`, `is_deleted`, `created_by`, `created_time`, `updated_by`, `updated_time`) 
VALUES (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '管理员', 0, 1, NOW(), 1, NOW());

-- 初始化客户数据
INSERT INTO `customer` (`id`, `name`, `company_name`, `phone`, `email`, `status`, `source`, `remark`, `is_deleted`, `created_by`, `created_time`, `updated_by`, `updated_time`) VALUES
(1, '张三', 'ABC科技有限公司', '13800138000', '<EMAIL>', 1, '网络推广', '重要客户', 0, 1, '2023-11-01 10:30:00', 1, '2023-11-01 15:20:00'),
(2, '李四', 'XYZ贸易公司', '13900139000', '<EMAIL>', 0, '朋友推荐', '有合作意向', 0, 1, '2023-11-02 14:20:00', 1, '2023-11-02 14:20:00'),
(3, '王五', 'DEF制造企业', '13700137000', '<EMAIL>', 1, '展会', '技术实力强', 0, 1, '2023-11-03 09:15:00', 1, '2023-11-03 09:15:00'),
(4, '赵六', 'GHI服务公司', '13600136000', '<EMAIL>', 2, '电话营销', '已流失客户', 0, 1, '2023-10-15 16:45:00', 1, '2023-10-15 16:45:00'),
(5, '钱七', 'JKL咨询公司', '13500135000', '<EMAIL>', 1, '朋友推荐', '咨询需求明确', 0, 1, '2023-11-05 11:30:00', 1, '2023-11-05 11:30:00');

-- 初始化互动记录数据
INSERT INTO `interaction` (`id`, `type`, `summary`, `next_follow_up_time`, `customer_id`, `is_deleted`, `created_by`, `created_time`, `updated_by`, `updated_time`) VALUES
(1, 0, '电话沟通产品需求，客户表示有兴趣，需要进一步了解技术细节', '2023-12-05 14:00:00', 1, 0, 1, '2023-11-01 16:30:00', 1, '2023-11-01 16:30:00'),
(2, 1, '发送产品介绍邮件，包含详细技术规格和价格信息', '2023-12-03 10:00:00', 1, 0, 1, '2023-11-02 09:20:00', 1, '2023-11-02 09:20:00'),
(3, 2, '现场会议讨论合作方案，客户对价格有异议', '2023-12-06 15:30:00', 1, 0, 1, '2023-11-03 14:45:00', 1, '2023-11-03 14:45:00'),
(4, 0, '初次电话联系，了解客户基本需求', '2023-12-04 09:00:00', 2, 0, 1, '2023-11-02 15:10:00', 1, '2023-11-02 15:10:00'),
(5, 0, '跟进电话，客户表示需要内部讨论', '2023-12-07 14:00:00', 3, 0, 1, '2023-11-04 10:20:00', 1, '2023-11-04 10:20:00'),
(6, 1, '发送报价单，等待客户反馈', '2023-12-08 16:00:00', 3, 0, 1, '2023-11-05 13:30:00', 1, '2023-11-05 13:30:00'),
(7, 0, '最后一次联系，客户表示暂不考虑', NULL, 4, 0, 1, '2023-10-20 11:00:00', 1, '2023-10-20 11:00:00'),
(8, 2, '面对面会议，详细介绍解决方案', '2023-12-09 10:30:00', 5, 0, 1, '2023-11-06 16:15:00', 1, '2023-11-06 16:15:00');
