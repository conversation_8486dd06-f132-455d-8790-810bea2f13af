spring:
  datasource:
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: jdbc:mysql://*************:50006/gsms_internet_iportal?useUnicode=true&characterEncoding=utf-8&useSSL=false
      username: dbuser
      password: ENC(o+X5c69CprITIon4KwVAu8caTEEACs4P/LluTOWldSA=)
      initial-size: 1
      max-active: 100
      pool-prepared-statements: true
      min-idle: 1
      max-wait: 15000
      keep-alive: true
    type: com.alibaba.druid.pool.DruidDataSource

jasypt:
  encryptor:
    password: ${JASYPT_KEY:ZYSO96CMPZDUUKLVSLO2GW7PL}
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    property:
      prefix: ENC(
      suffix: )



