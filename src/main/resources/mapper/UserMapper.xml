<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.why.dao.UserDao">

    <!-- 根据用户名查询用户信息 -->
    <select id="selectByUsername" resultType="com.cnpc.why.entity.UserEntity">
        SELECT id, username, password, real_name, is_deleted, created_by, created_time, updated_by, updated_time
        FROM user
        WHERE username = #{username}
          AND is_deleted = 0
    </select>

</mapper>
