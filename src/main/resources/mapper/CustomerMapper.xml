<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.why.dao.CustomerDao">

    <!-- 分页查询客户列表 -->
    <select id="selectCustomerPage" resultType="com.cnpc.why.dto.CustomerDTO">
        SELECT 
            id,
            name,
            company_name AS companyName,
            phone,
            email,
            status,
            source,
            remark,
            DATE_FORMAT(created_time, '%Y-%m-%d %H:%i:%s') AS createdTime
        FROM customer
        WHERE is_deleted = 0
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') OR company_name LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        ORDER BY created_time DESC
    </select>

    <!-- 获取客户选项列表 -->
    <select id="selectCustomerOptions" resultType="com.cnpc.why.dto.CustomerOptionDTO">
        SELECT 
            id AS value,
            CONCAT(name, '-', company_name) AS label
        FROM customer
        WHERE is_deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 根据日期查询新增客户列表 -->
    <select id="selectCustomersByDate" resultType="com.cnpc.why.dto.CustomerDTO">
        SELECT 
            id,
            name,
            company_name AS companyName,
            phone,
            email,
            source
        FROM customer
        WHERE is_deleted = 0
          AND DATE(created_time) = #{date}
        ORDER BY created_time DESC
    </select>

    <!-- 统计已流失客户占比 -->
    <select id="selectLostCustomerRate" resultType="java.lang.Double">
        SELECT 
            CASE 
                WHEN COUNT(*) = 0 THEN 0.0
                ELSE ROUND(SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2)
            END
        FROM customer
        WHERE is_deleted = 0
    </select>

    <!-- 获取最近无新增客户天数 -->
    <select id="selectNoNewCustomerDays" resultType="java.lang.Integer">
        SELECT 
            CASE 
                WHEN MAX(DATE(created_time)) IS NULL THEN 999
                ELSE DATEDIFF(CURDATE(), MAX(DATE(created_time)))
            END
        FROM customer
        WHERE is_deleted = 0
    </select>

    <!-- 获取近30日新增客户趋势数据 -->
    <select id="selectCustomerTrendData" resultType="com.cnpc.why.dao.CustomerDao$CustomerTrendData">
        SELECT 
            DATE(created_time) AS date,
            COUNT(*) AS count
        FROM customer
        WHERE is_deleted = 0
          AND created_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(created_time)
        ORDER BY date ASC
    </select>

</mapper>
