<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.why.dao.InteractionDao">

    <!-- 分页查询互动记录列表 -->
    <select id="selectInteractionPage" resultType="com.cnpc.why.dto.InteractionDTO">
        SELECT 
            i.id,
            i.type,
            i.summary,
            DATE_FORMAT(i.next_follow_up_time, '%Y-%m-%d %H:%i:%s') AS nextFollowUpTime,
            i.customer_id AS customerId,
            c.name AS customerName,
            DATE_FORMAT(i.created_time, '%Y-%m-%d %H:%i:%s') AS createdTime
        FROM interaction i
        LEFT JOIN customer c ON i.customer_id = c.id AND c.is_deleted = 0
        WHERE i.is_deleted = 0
        <if test="type != null">
            AND i.type = #{type}
        </if>
        <if test="customerId != null">
            AND i.customer_id = #{customerId}
        </if>
        <if test="keyword != null and keyword != ''">
            AND i.summary LIKE CONCAT('%', #{keyword}, '%')
        </if>
        ORDER BY i.created_time DESC
    </select>

    <!-- 根据ID查询互动记录详情 -->
    <select id="selectInteractionDetailById" resultType="com.cnpc.why.dto.InteractionDTO">
        SELECT 
            i.id,
            i.type,
            i.summary,
            DATE_FORMAT(i.next_follow_up_time, '%Y-%m-%d %H:%i:%s') AS nextFollowUpTime,
            i.customer_id AS customerId,
            c.name AS customerName,
            DATE_FORMAT(i.created_time, '%Y-%m-%d %H:%i:%s') AS createdTime,
            DATE_FORMAT(i.updated_time, '%Y-%m-%d %H:%i:%s') AS updatedTime
        FROM interaction i
        LEFT JOIN customer c ON i.customer_id = c.id AND c.is_deleted = 0
        WHERE i.id = #{id}
          AND i.is_deleted = 0
    </select>

</mapper>
