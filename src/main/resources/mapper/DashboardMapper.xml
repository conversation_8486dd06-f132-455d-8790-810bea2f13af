<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cnpc.why.dao.DashboardDao">

    <!-- 分页查询高价值客户列表 -->
    <select id="selectValuableCustomerPage" resultType="com.cnpc.why.dto.ValuableCustomerDTO">
        SELECT 
            id,
            name,
            company_name AS companyName,
            interaction_count AS interactionCount,
            DATE_FORMAT(last_interaction_time, '%Y-%m-%d %H:%i:%s') AS lastInteractionTime
        FROM valuable_customer_view
        ORDER BY interaction_count DESC, last_interaction_time DESC
    </select>

    <!-- 查询所有高价值客户列表（用于导出） -->
    <select id="selectAllValuableCustomers" resultType="com.cnpc.why.dto.ValuableCustomerDTO">
        SELECT 
            id,
            name,
            company_name AS companyName,
            interaction_count AS interactionCount,
            DATE_FORMAT(last_interaction_time, '%Y-%m-%d %H:%i:%s') AS lastInteractionTime
        FROM valuable_customer_view
        ORDER BY interaction_count DESC, last_interaction_time DESC
    </select>

</mapper>
