# 后端开发技术栈和代码结构规范

### 1 包组织说明
- **com.cnpc.why.controller** 存放所有controller类
- **com.cnpc.why.dao** 存放所有dao类
- **com.cnpc.why.dto** 存放返回给前端的响应对象
- **com.cnpc.why.entity** 存放所有的数据库实体类
- **com.cnpc.why.request** 存放前端请求对象
- **com.cnpc.why.service** 存放业务处理类
- **com.cnpc.why.inteceptor** 存放业务处理类


## 2. 技术栈

### 2.1 核心框架

- **Spring boot**: 主开发框架
- **MyBatis Plus**: ORM框架
- **Maven**: 项目构建和依赖管理

## 3. 代码规范

### 3.1 命名规范

#### 类命名

- **前端响应对象**: 以DTO结尾，如`ConfigCreateReqDTO`
- **前端请求对象**: 以Request结尾，如`CancelDriverAccountRequest`
- **Service**: 以Service结尾，如`AccountService`
- **Controller**: 以Controller结尾，如`AccountController`
- **Dao**: 以Dao结尾，如`UserDao`
- **数据库实体类**: 以Entity结尾，如`UserEntity`

#### 方法命名

- **规范**: 采用动宾结构命名
- **查询**: get/query/list/page开头
- **新增**: create/add/save开头
- **修改**: update/modify开头
- **删除**: delete/remove开头
- **校验**: validate/check开头
- **批量**: 批量操作方法以batch开头

## 4. 编码规范

### 4.2 异常处理规范

- 统一使用com.cnpc.why.exception.BizException类创建业务异常
- 错误码统一6位数字，如000001，不得与其他错误码重复

```java
// 抛出业务异常示例
throw new BizException("000001", "用户不存在");
```


### 4.3 controller规范

- 所有接口均采用POST方式
- 所有接口均属于application/json调用
- Controller只做参数接收和返回，不处理业务逻辑
- 统一使用com.cnpc.why.dto.ResponseDTO包装前端响应对象

```java
// 标准controller方法定义示例
import com.cnpc.why.dto.ResponseDTO;

@PostMapping("/manager/billdetails")
ResponseDTO<BillDetailsPageDTO> billDetails(@RequestBody @Valid BillDetailsGsmsPageRequest request) {
    return accountManager.billDetails(request);
}

@PostMapping("/manager/billdetails")
ResponseDTO<Void> billDetails(@RequestBody @Valid BillDetailsGsmsPageRequest request) {
  return accountManager.billDetails(null);
}
```



