# 客户管理系统后端接口文档

## 1. 接口通用说明

### 1.1 请求方式
- 所有接口均使用 **POST** 请求方式
- 请求地址：`http://127.0.0.1:8080/`

### 1.2 通用响应格式
```json
{
  "success": true,          // 请求处理状态，true=成功，false=失败
  "data": {},              // 响应的业务数据，仅当success为true时有值
  "errorCode": "000001",   // success为false时的错误码
  "message": "请求成功"     // success为true时固定为"请求成功"
}
```

### 1.3 分页参数格式
```json
{
  "pageNum": 1,    // 当前页码，默认1
  "pageSize": 10   // 每页大小，默认10
}
```

### 1.4 分页响应格式
```json
{
  "rows": [],      // 数据列表
  "pageNum": 1,    // 当前页码
  "pageSize": 10,  // 每页大小
  "pageSum": 5,    // 总页数
  "total": 42      // 总记录数
}
```

---

## 2. 用户认证接口

### 2.1 用户登录
- **接口名称**：用户登录
- **接口url**：/user/login
- **请求方式**：POST

**请求参数说明**
- username (String, 必填): 用户账号
- password (String, 必填): 用户密码

**请求数据示例**
```json
{
  "username": "admin",
  "password": "123456"
}
```

**响应参数说明**
- token (String): 认证令牌
- userInfo (Object): 用户信息
  - id (Long): 用户ID
  - username (String): 用户账号
  - realName (String): 用户真实姓名

**响应数据示例**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": 1,
      "username": "admin",
      "realName": "管理员"
    }
  },
  "message": "请求成功"
}
```

---

## 3. 客户管理接口

### 3.1 分页查询客户列表
- **接口名称**：分页查询客户列表
- **接口url**：/customer/list
- **请求方式**：POST

**请求参数说明**
- pageNum (Long, 可选): 当前页码，默认1
- pageSize (Long, 可选): 每页大小，默认10
- status (Integer, 可选): 客户状态筛选，0=潜在客户，1=已联系客户，2=已流失
- keyword (String, 可选): 关键词搜索，支持客户姓名/公司名称模糊查询

**请求数据示例**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "status": 1,
  "keyword": "张三"
}
```

**响应参数说明**
CustomerPageDTO（客户分页信息）:
- rows (List<CustomerDTO>): 客户列表
- pageNum (Long): 当前页码
- pageSize (Long): 每页大小
- pageSum (Long): 总页数
- total (Long): 总记录数

CustomerDTO（客户信息）:
- id (Long): 客户ID
- name (String): 客户姓名
- companyName (String): 公司名称
- phone (String): 电话
- email (String): 邮箱
- status (Integer): 状态，0=潜在客户，1=已联系客户，2=已流失
- source (String): 来源
- remark (String): 备注
- createdTime (String): 创建时间

**响应数据示例**
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 1,
        "name": "张三",
        "companyName": "ABC科技有限公司",
        "phone": "13800138000",
        "email": "<EMAIL>",
        "status": 1,
        "source": "网络推广",
        "remark": "重要客户",
        "createdTime": "2023-12-01 10:30:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "pageSum": 1,
    "total": 1
  },
  "message": "请求成功"
}
```

### 3.2 新增客户
- **接口名称**：新增客户
- **接口url**：/customer/add
- **请求方式**：POST

**请求参数说明**
- name (String, 必填): 客户姓名
- companyName (String, 必填): 公司名称
- phone (String, 可选): 电话
- email (String, 可选): 邮箱
- status (Integer, 必填): 状态，0=潜在客户，1=已联系客户，2=已流失
- source (String, 可选): 来源
- remark (String, 可选): 备注

**请求数据示例**
```json
{
  "name": "李四",
  "companyName": "XYZ贸易公司",
  "phone": "13900139000",
  "email": "<EMAIL>",
  "status": 0,
  "source": "朋友推荐",
  "remark": "有合作意向"
}
```

**响应数据示例**
```json
{
  "success": true,
  "data": null,
  "message": "请求成功"
}
```

### 3.3 获取客户详情
- **接口名称**：获取客户详情
- **接口url**：/customer/detail
- **请求方式**：POST

**请求参数说明**
- id (Long, 必填): 客户ID

**请求数据示例**
```json
{
  "id": 1
}
```

**响应参数说明**
CustomerDTO（客户详情信息）:
- id (Long): 客户ID
- name (String): 客户姓名
- companyName (String): 公司名称
- phone (String): 电话
- email (String): 邮箱
- status (Integer): 状态
- source (String): 来源
- remark (String): 备注
- createdTime (String): 创建时间
- updatedTime (String): 更新时间

**响应数据示例**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "张三",
    "companyName": "ABC科技有限公司",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "status": 1,
    "source": "网络推广",
    "remark": "重要客户",
    "createdTime": "2023-12-01 10:30:00",
    "updatedTime": "2023-12-01 15:20:00"
  },
  "message": "请求成功"
}
```

### 3.4 更新客户信息
- **接口名称**：更新客户信息
- **接口url**：/customer/update
- **请求方式**：POST

**请求参数说明**
- id (Long, 必填): 客户ID
- name (String, 必填): 客户姓名
- companyName (String, 必填): 公司名称
- phone (String, 可选): 电话
- email (String, 可选): 邮箱
- status (Integer, 必填): 状态
- source (String, 可选): 来源
- remark (String, 可选): 备注

**请求数据示例**
```json
{
  "id": 1,
  "name": "张三",
  "companyName": "ABC科技有限公司",
  "phone": "13800138001",
  "email": "<EMAIL>",
  "status": 1,
  "source": "网络推广",
  "remark": "重要客户，已签约"
}
```

**响应数据示例**
```json
{
  "success": true,
  "data": null,
  "message": "请求成功"
}
```

### 3.5 删除客户
- **接口名称**：删除客户
- **接口url**：/customer/delete
- **请求方式**：POST

**请求参数说明**
- id (Long, 必填): 客户ID

**请求数据示例**
```json
{
  "id": 1
}
```

**响应数据示例**
```json
{
  "success": true,
  "data": null,
  "message": "请求成功"
}
```

### 3.6 获取客户选项列表
- **接口名称**：获取客户选项列表（用于下拉框）
- **接口url**：/customer/options
- **请求方式**：POST

**请求参数说明**
无参数

**请求数据示例**
```json
{}
```

**响应参数说明**
CustomerOptionDTO（客户选项信息）:
- value (Long): 客户ID
- label (String): 客户显示名称（姓名-公司名称）

**响应数据示例**
```json
{
  "success": true,
  "data": [
    {
      "value": 1,
      "label": "张三-ABC科技有限公司"
    },
    {
      "value": 2,
      "label": "李四-XYZ贸易公司"
    }
  ],
  "message": "请求成功"
}
```

---

## 4. 互动记录管理接口

### 4.1 分页查询互动记录列表
- **接口名称**：分页查询互动记录列表
- **接口url**：/interaction/list
- **请求方式**：POST

**请求参数说明**
- pageNum (Long, 可选): 当前页码，默认1
- pageSize (Long, 可选): 每页大小，默认10
- type (Integer, 可选): 互动类型筛选，0=电话，1=邮件，2=会议
- customerId (Long, 可选): 关联客户ID筛选
- keyword (String, 可选): 关键词搜索，支持内容摘要模糊查询

**请求数据示例**
```json
{
  "pageNum": 1,
  "pageSize": 10,
  "type": 0,
  "customerId": 1,
  "keyword": "产品介绍"
}
```

**响应参数说明**
InteractionPageDTO（互动记录分页信息）:
- rows (List<InteractionDTO>): 互动记录列表
- pageNum (Long): 当前页码
- pageSize (Long): 每页大小
- pageSum (Long): 总页数
- total (Long): 总记录数

InteractionDTO（互动记录信息）:
- id (Long): 互动记录ID
- type (Integer): 互动类型，0=电话，1=邮件，2=会议
- summary (String): 内容摘要
- nextFollowUpTime (String): 下次跟进时间
- customerId (Long): 关联客户ID
- customerName (String): 关联客户姓名
- createdTime (String): 创建时间

**响应数据示例**
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 1,
        "type": 0,
        "summary": "电话沟通产品需求，客户表示有兴趣，需要进一步了解技术细节",
        "nextFollowUpTime": "2023-12-05 14:00:00",
        "customerId": 1,
        "customerName": "张三",
        "createdTime": "2023-12-01 16:30:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "pageSum": 1,
    "total": 1
  },
  "message": "请求成功"
}
```

### 4.2 新增互动记录
- **接口名称**：新增互动记录
- **接口url**：/interaction/add
- **请求方式**：POST

**请求参数说明**
- customerId (Long, 必填): 关联客户ID
- type (Integer, 必填): 互动类型，0=电话，1=邮件，2=会议
- summary (String, 必填): 内容摘要
- nextFollowUpTime (String, 可选): 下次跟进时间，格式：YYYY-MM-DD HH:mm:ss

**请求数据示例**
```json
{
  "customerId": 1,
  "type": 0,
  "summary": "电话沟通产品需求，客户表示有兴趣，需要进一步了解技术细节和价格信息",
  "nextFollowUpTime": "2023-12-05 14:00:00"
}
```

**响应数据示例**
```json
{
  "success": true,
  "data": null,
  "message": "请求成功"
}
```

### 4.3 获取互动记录详情
- **接口名称**：获取互动记录详情
- **接口url**：/interaction/detail
- **请求方式**：POST

**请求参数说明**
- id (Long, 必填): 互动记录ID

**请求数据示例**
```json
{
  "id": 1
}
```

**响应参数说明**
InteractionDTO（互动记录详情信息）:
- id (Long): 互动记录ID
- type (Integer): 互动类型
- summary (String): 内容摘要
- nextFollowUpTime (String): 下次跟进时间
- customerId (Long): 关联客户ID
- customerName (String): 关联客户姓名
- createdTime (String): 创建时间
- updatedTime (String): 更新时间

**响应数据示例**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "type": 0,
    "summary": "电话沟通产品需求，客户表示有兴趣，需要进一步了解技术细节",
    "nextFollowUpTime": "2023-12-05 14:00:00",
    "customerId": 1,
    "customerName": "张三",
    "createdTime": "2023-12-01 16:30:00",
    "updatedTime": "2023-12-01 16:30:00"
  },
  "message": "请求成功"
}
```

### 4.4 更新互动记录
- **接口名称**：更新互动记录
- **接口url**：/interaction/update
- **请求方式**：POST

**请求参数说明**
- id (Long, 必填): 互动记录ID
- customerId (Long, 必填): 关联客户ID
- type (Integer, 必填): 互动类型
- summary (String, 必填): 内容摘要
- nextFollowUpTime (String, 可选): 下次跟进时间

**请求数据示例**
```json
{
  "id": 1,
  "customerId": 1,
  "type": 0,
  "summary": "电话沟通产品需求，客户表示有兴趣，已发送详细方案",
  "nextFollowUpTime": "2023-12-08 10:00:00"
}
```

**响应数据示例**
```json
{
  "success": true,
  "data": null,
  "message": "请求成功"
}
```

### 4.5 删除互动记录
- **接口名称**：删除互动记录
- **接口url**：/interaction/delete
- **请求方式**：POST

**请求参数说明**
- id (Long, 必填): 互动记录ID

**请求数据示例**
```json
{
  "id": 1
}
```

**响应数据示例**
```json
{
  "success": true,
  "data": null,
  "message": "请求成功"
}
```

---

## 5. 智能分析接口

### 5.1 获取仪表板数据
- **接口名称**：获取仪表板数据
- **接口url**：/dashboard/data
- **请求方式**：POST

**请求参数说明**
无参数

**请求数据示例**
```json
{}
```

**响应参数说明**
DashboardDataDTO（仪表板数据）:
- alerts (Object): 预警信息
  - lostCustomerRate (Double): 已流失客户占比
  - noNewCustomerDays (Integer): 无新增客户天数
  - showAlert (Boolean): 是否显示预警
- customerTrend (Object): 客户趋势数据
  - dates (List<String>): 日期列表
  - counts (List<Integer>): 对应日期的新增客户数量

**响应数据示例**
```json
{
  "success": true,
  "data": {
    "alerts": {
      "lostCustomerRate": 35.5,
      "noNewCustomerDays": 8,
      "showAlert": true
    },
    "customerTrend": {
      "dates": ["2023-11-01", "2023-11-02", "2023-11-03", "2023-11-04", "2023-11-05"],
      "counts": [3, 5, 2, 8, 4]
    }
  },
  "message": "请求成功"
}
```

### 5.2 获取高价值客户列表
- **接口名称**：获取高价值客户列表
- **接口url**：/dashboard/valuable-customers
- **请求方式**：POST

**请求参数说明**
- pageNum (Long, 可选): 当前页码，默认1
- pageSize (Long, 可选): 每页大小，默认10

**请求数据示例**
```json
{
  "pageNum": 1,
  "pageSize": 10
}
```

**响应参数说明**
ValuableCustomerPageDTO（高价值客户分页信息）:
- rows (List<ValuableCustomerDTO>): 高价值客户列表
- pageNum (Long): 当前页码
- pageSize (Long): 每页大小
- pageSum (Long): 总页数
- total (Long): 总记录数

ValuableCustomerDTO（高价值客户信息）:
- id (Long): 客户ID
- name (String): 客户姓名
- companyName (String): 公司名称
- interactionCount (Integer): 互动次数
- lastInteractionTime (String): 上次联系时间

**响应数据示例**
```json
{
  "success": true,
  "data": {
    "rows": [
      {
        "id": 1,
        "name": "张三",
        "companyName": "ABC科技有限公司",
        "interactionCount": 8,
        "lastInteractionTime": "2023-12-01 16:30:00"
      }
    ],
    "pageNum": 1,
    "pageSize": 10,
    "pageSum": 1,
    "total": 1
  },
  "message": "请求成功"
}
```

### 5.3 获取指定日期新增客户详情
- **接口名称**：获取指定日期新增客户详情
- **接口url**：/dashboard/daily-customers
- **请求方式**：POST

**请求参数说明**
- date (String, 必填): 查询日期，格式：YYYY-MM-DD

**请求数据示例**
```json
{
  "date": "2023-12-01"
}
```

**响应参数说明**
List<CustomerDTO>（客户信息列表）:
- id (Long): 客户ID
- name (String): 客户姓名
- companyName (String): 公司名称
- phone (String): 电话
- email (String): 邮箱
- source (String): 来源

**响应数据示例**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "张三",
      "companyName": "ABC科技有限公司",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "source": "网络推广"
    }
  ],
  "message": "请求成功"
}
```

### 5.4 导出高价值客户Excel
- **接口名称**：导出高价值客户Excel
- **接口url**：/dashboard/export-valuable-customers
- **请求方式**：POST

**请求参数说明**
无参数

**请求数据示例**
```json
{}
```

**响应说明**
返回Excel文件流，Content-Type为：application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

---

## 6. 状态枚举定义

### 6.1 客户状态 (status)
客户状态枚举值定义:
- 0: 潜在客户
- 1: 已联系客户
- 2: 已流失

### 6.2 互动类型 (type)
互动类型枚举值定义:
- 0: 电话
- 1: 邮件
- 2: 会议

---

## 7. 错误码定义

### 7.1 通用错误码
- 000001: 系统异常
- 000002: 参数错误
- 000003: 数据不存在
- 000004: 权限不足

### 7.2 用户相关错误码
- 001001: 用户名或密码错误
- 001002: 用户不存在
- 001003: 用户已被禁用
- 001004: 登录令牌无效
- 001005: 登录令牌已过期

### 7.3 客户相关错误码
- 002001: 客户姓名不能为空
- 002002: 公司名称不能为空
- 002003: 客户状态无效
- 002004: 客户不存在
- 002005: 客户已被删除

### 7.4 互动记录相关错误码
- 003001: 关联客户不能为空
- 003002: 互动类型无效
- 003003: 内容摘要不能为空
- 003004: 互动记录不存在
- 003005: 互动记录已被删除

---

## 8. 数据库字段映射说明

### 8.1 客户表字段映射
- id → id
- name → name
- company_name → companyName
- phone → phone
- email → email
- status → status
- source → source
- remark → remark
- created_time → createdTime
- updated_time → updatedTime

### 8.2 互动记录表字段映射
- id → id
- type → type
- summary → summary
- next_follow_up_time → nextFollowUpTime
- customer_id → customerId
- created_time → createdTime
- updated_time → updatedTime

### 8.3 用户表字段映射
- id → id
- username → username
- real_name → realName
- created_time → createdTime
- updated_time → updatedTime

---

## 9. 接口调用示例

### 9.1 完整的客户管理流程示例

**步骤1：用户登录**
```bash
curl -X POST http://127.0.0.1:8080/user/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

**步骤2：获取客户列表**
```bash
curl -X POST http://127.0.0.1:8080/customer/list \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"pageNum":1,"pageSize":10}'
```

**步骤3：新增客户**
```bash
curl -X POST http://127.0.0.1:8080/customer/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"name":"张三","companyName":"ABC科技","status":0}'
```

**步骤4：新增互动记录**
```bash
curl -X POST http://127.0.0.1:8080/interaction/add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"customerId":1,"type":0,"summary":"电话沟通需求"}'
```

### 9.2 智能分析数据获取示例

**获取仪表板数据**
```bash
curl -X POST http://127.0.0.1:8080/dashboard/data \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{}'
```

**获取高价值客户列表**
```bash
curl -X POST http://127.0.0.1:8080/dashboard/valuable-customers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"pageNum":1,"pageSize":10}'
```

---

## 10. 注意事项

### 10.1 认证说明
- 除登录接口外，所有接口都需要在请求头中携带认证令牌
- 令牌格式：`Authorization: Bearer {token}`
- 令牌有效期为24小时，过期后需要重新登录

### 10.2 数据验证
- 所有必填字段都会进行非空验证
- 邮箱字段会进行格式验证
- 手机号字段会进行格式验证（中国大陆手机号）
- 日期时间字段格式为：YYYY-MM-DD HH:mm:ss

### 10.3 分页说明
- 页码从1开始
- 默认每页10条记录
- 最大每页100条记录
- 总页数计算：pageSum = Math.ceil(total / pageSize)

### 10.4 软删除说明
- 客户和互动记录采用软删除机制
- 删除操作只是将is_deleted字段设置为1
- 查询时会自动过滤已删除的记录

### 10.5 时区说明
- 所有时间字段均使用服务器本地时区
- 建议前端显示时间时转换为用户本地时区
